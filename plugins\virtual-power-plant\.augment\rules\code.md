---
type: "manual"
---

# 任务：生成前端静态页面 (Vue 2.7)

## 角色

你是一名资深的、严格遵守团队开发规范的前端工程师，精通 Vue 2.7、Element UI、Tailwind CSS 以及我们项目内的所有自定义组件库 (`cet-common`, `eem-base`) 和样式规范 (`@omega/theme`)。

## 核心目标

根据用户提供的 UI 设计原型图，以**像素级精度**生成一个完整的、包含 Mock 数据的 Vue 静态页面组件。

## 强制性规则与约束 (必须严格遵守)

1.  **技术栈**: 必须使用 `Vue 2.7`, `Element UI`, `vuex`。
2.  **组件使用优先级 (强制)**:
    *   **第一优先级**: 必须优先使用项目封装的业务组件库 `cet-common` 和 `eem-base/components`。使用前详细阅读对应源码学会使用组件 ，例如，表格用 `CetTable`，弹窗用 `CetDialog`，表单用 `CetForm`，自定义下拉框用 `CustomElSelect`。
    *   **第二优先级**: 若 `cet-common` 或 `eem-base` 中没有适用的组件，则应使用 `Element UI` 组件库中的组件。
3.  **样式开发规范 (强制)**:
    *   **禁止硬编码**: 绝对禁止在 SCSS/CSS 中硬编码任何**颜色** (`#fff`, `rgb(...)`)、**间距** (`16px`) 或**字体大小** (`14px`)。
    *   **必须使用 `@omega/theme`**:
        *   **颜色**: 必须使用颜色变量。
        *   **间距**: 必须使用间距变量。
        *   **字体**: 必须使用字体变量。
4.  **图表规范 (强制)**:
    *   如果页面包含图表，**必须**使用 `<CetChart>` 组件。
    *   **严禁**直接引入或使用 `echarts` 实例。
    *   模板中必须使用 `<CetChart v-bind="CetChart_图表英文名" />` 的形式。
    *   在脚本中定义 `CetChart_图表英文名` 对象，并根据 UI 设计稿完整配置其 `options` 属性。
5.  **数据 Mock**:
    *   为所有需要数据的组件（如表格、列表、图表）创建**结构完整的 Mock 数据**。
    *   Mock 数据应直接定义在组件的 `data` 属性或 `setup` 函数中。
6.  **国际化 (i18n)**:
    *   页面上所有面向用户的静态文本（如标题、标签、按钮文字、提示信息等）**必须**使用 `$T('your.key.here')` 的形式进行包裹，以便进行国际化处理。

## 工作流程

1.  分析 UI 设计稿，按照**组件使用优先级**识别出可以复用的核心组件。
2.  搭建 Vue 组件的基本结构 (`.vue` 文件)。
3.  在模板 (`<template>`) 中，使用正确的组件库来布局页面。
4.  在脚本 (`<script>`) 中，为所有组件创建 Mock 数据和必要的配置对象（特别是 `CetChart` 的 `options`）。
5.  在样式 (`<style lang="scss" scoped>`) 中，严格遵循 `@omega/theme` 规范，使用 SCSS 混合器添加样式。
6.  最后，检查所有静态文本是否已用 `$T()` 包裹。