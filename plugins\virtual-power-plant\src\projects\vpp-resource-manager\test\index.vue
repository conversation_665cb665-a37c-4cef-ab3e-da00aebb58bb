<template>
  <div class="vpp-resource-page">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-controls">
        <!-- 搜索框 -->
        <div class="search-input-wrapper">
          <el-input
            v-model="searchKeyword"
            :placeholder="$T('请输入关键字')"
            prefix-icon="el-icon-search"
            clearable
          />
        </div>

        <!-- 机组类型选择器 -->
        <CustomElSelect
          :prefix_in="$T('机组类型')"
          v-model="selectedType"
          clearable
        >
          <el-option :label="$T('全部')" value=""></el-option>
          <el-option :label="$T('调峰机组')" value="调峰机组"></el-option>
          <el-option :label="$T('基荷机组')" value="基荷机组"></el-option>
          <el-option :label="$T('备用机组')" value="备用机组"></el-option>
        </CustomElSelect>
      </div>

      <!-- 新增按钮 -->
      <el-button
        type="primary"
        @click="handleAdd"
        icon="el-icon-plus"
      >
        {{ $T('新增') }}
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <CetTable
        :data="tableData"
        :showPagination="false"
        :border="true"
        :highlightCurrentRow="true"
        @currentChange_out="handleCurrentChange"
      >
        <!-- 序号列 -->
        <el-table-column
          type="index"
          :label="$T('序号')"
          width="80"
          align="center"
        />

        <!-- 机组名称列 -->
        <el-table-column
          prop="name"
          :label="$T('机组名称')"
          min-width="200"
        />

        <!-- 机组类型列 -->
        <el-table-column
          prop="type"
          :label="$T('机组类型')"
          width="150"
        />

        <!-- 操作列 -->
        <el-table-column
          :label="$T('操作')"
          width="200"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleDetail(scope.row)"
              class="operation-btn"
            >
              {{ $T('详情') }}
            </el-button>
            <el-button
              type="text"
              @click="handleEdit(scope.row)"
              class="operation-btn"
            >
              {{ $T('编辑') }}
            </el-button>
            <el-button
              type="text"
              @click="handleDelete(scope.row)"
              class="operation-btn danger-text"
            >
              {{ $T('删除') }}
            </el-button>
          </template>
        </el-table-column>
      </CetTable>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VppResourceManager',
  data() {
    return {
      // 搜索条件
      searchKeyword: '',
      selectedType: '',

      // 表格数据
      tableData: [
        { id: 1, name: '1#调峰机组', type: '调峰机组' },
        { id: 2, name: '2#调峰机组', type: '调峰机组' },
        { id: 3, name: '3#调峰机组', type: '调峰机组' },
        { id: 4, name: '4#调峰机组', type: '调峰机组' },
        { id: 5, name: '5#调峰机组', type: '调峰机组' },
        { id: 6, name: '6#调峰机组', type: '调峰机组' },
        { id: 7, name: '7#调峰机组', type: '调峰机组' },
        { id: 8, name: '8#调峰机组', type: '调峰机组' },
        { id: 9, name: '9#调峰机组', type: '调峰机组' },
        { id: 10, name: '10#调峰机组', type: '调峰机组' },
        { id: 11, name: '1#基荷机组', type: '基荷机组' },
        { id: 12, name: '2#基荷机组', type: '基荷机组' },
        { id: 13, name: '1#备用机组', type: '备用机组' },
        { id: 14, name: '2#备用机组', type: '备用机组' },
        { id: 15, name: '3#备用机组', type: '备用机组' }
      ]
    };
  },

  methods: {
    /**
     * 查看详情
     */
    handleDetail(row) {
      console.log('查看详情:', row);
      this.$message.info(`${this.$T('查看详情')}: ${row.name}`);
    },

    /**
     * 编辑机组
     */
    handleEdit(row) {
      console.log('编辑机组:', row);
      this.$message.info(`${this.$T('编辑机组')}: ${row.name}`);
    },

    /**
     * 删除机组
     */
    handleDelete(row) {
      this.$confirm(
        `${this.$T('确认删除')} ${row.name}?`,
        this.$T('警告'),
        {
          confirmButtonText: this.$T('确认'),
          cancelButtonText: this.$T('取消'),
          type: 'warning'
        }
      ).then(() => {
        console.log('删除机组:', row);
        this.$message.success(this.$T('删除成功'));
      }).catch(() => {
        this.$message.info(this.$T('已取消删除'));
      });
    },

    /**
     * 新增机组
     */
    handleAdd() {
      console.log('新增机组');
      this.$message.info(this.$T('新增机组'));
    },

    /**
     * 当前行改变
     */
    handleCurrentChange(row) {
      console.log('当前行:', row);
    }
  }
};
</script>

<style lang="scss" scoped>
.vpp-resource-page {
  @include padding(J4); // 24px
  @include background_color(BG);
  
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  @include margin_bottom(J3); // 16px
  @include padding(J3); // 16px
  @include background_color(BG1);
  border-radius: mh-get(Ra); // 4px
}

.search-controls {
  display: flex;
  align-items: center;
  gap: mh-get(J3); // 16px
}

.search-input-wrapper {
  width: 240px;
}

.table-section {
  @include background_color(BG1);
  border-radius: mh-get(Ra); // 4px
  @include padding(J4); // 24px
}

.operation-btn {
  @include margin_right(J4); // 24px
  @include font_color(ZS);

  &:last-child {
    margin-right: 0;
  }

  &:hover {
    @include font_color(F1);
  }
}

.danger-text {
  @include font_color(Sta3); // 危险色

  &:hover {
    @include font_color(Sta3);
    opacity: 0.8;
  }
}

// 自定义表格样式
:deep(.el-table) {
  @include background_color(BG1);

  .el-table__header {
    @include background_color(BG5);

    th {
      @include background_color(BG5);
      @include font_color(T1);
      @include font_size(Ab); // 12px
      font-weight: 400;
    }
  }

  .el-table__body {
    tr {
      @include background_color(BG1);

      &:hover {
        @include background_color(BG2);
      }

      td {
        @include font_color(T2);
        @include font_size(Aa); // 14px
        @include border_color(B2);
      }
    }
  }
}


</style>